import { createFileRoute, useRouter } from '@tanstack/react-router'
import { Link } from '@tanstack/react-router'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AlertCircle } from "lucide-react"
import { toast } from "sonner"
import { useEffect } from "react"

export const Route = createFileRoute('/auth/error')({
  component: AuthErrorPage,
})

function AuthErrorPage() {
  const search = Route.useSearch() as { error?: string }
  const errorMessage = search.error || "An authentication error occurred"
  const router = useRouter()

  // Check if this is the "unable_to_create_user" error (account not found) and handle it with toast + redirect
  useEffect(() => {
    if (errorMessage === "unable_to_create_user" || errorMessage.includes("Account not found")) {
      // Show toast notification
      toast.error("Account not found. Please sign up using the mobile app first.", {
        duration: 5000,
        description: "You need to create an account through our mobile app before signing in to the web dashboard."
      })

      // Redirect to login page after a short delay
      setTimeout(() => {
        router.navigate({ to: "/login" })
      }, 1000)

      return
    }
  }, [errorMessage, router])

  // If it's the account not found error, show a loading state while redirecting
  if (errorMessage === "unable_to_create_user" || errorMessage.includes("Account not found")) {
    return (
      <div className="w-full min-h-screen relative bg-[#e9e5dc] dark:bg-[#1e1b16]">
        <div className="w-full h-full max-w-[1600px] mx-auto px-4 md:px-8 py-12 lg:py-8 flex flex-col lg:flex-row items-center justify-center gap-12 lg:gap-8 relative z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#7e7b76] mx-auto mb-4"></div>
            <p className="text-[#7e7b76] font-manrope_1">Redirecting to login...</p>
          </div>
        </div>
      </div>
    )
  }

  // For other errors, show the normal error page
  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 flex flex-col items-center">
          <div className="mb-4 flex items-center justify-center">
            <AlertCircle className="h-12 w-12 text-destructive" />
          </div>
          <CardTitle className="text-2xl font-bold text-center text-destructive">
            Authentication Error
          </CardTitle>
          <CardDescription className="text-center">
            There was a problem with your authentication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{errorMessage}</p>
          </div>

          <div className="flex flex-col space-y-2">
            <Button asChild>
              <Link to="/login">
                Try Again
              </Link>
            </Button>

            <Button variant="outline" asChild>
              <Link to="/">
                Go Home
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
