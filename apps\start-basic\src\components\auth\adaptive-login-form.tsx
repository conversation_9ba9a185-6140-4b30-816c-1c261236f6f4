// adaptive-login-form.tsx
import { useState, useEffect } from "react";
import { useRouter, useSearch } from "@tanstack/react-router";
import { GoogleLoginButton } from "@/components/auth/google-login-button";
import { GithubLoginButton } from "@/components/auth/github-login-button";
import { ArrowLeft } from "lucide-react";
import { Footer } from "@/components/general/footer";
import { toast } from "sonner";

interface AdaptiveLoginFormProps {
  callbackUrl?: string;
}

interface OAuthProvider {
  id: 'google' | 'github' | 'apple';
  name: string;
  component: React.ReactNode;
  available: boolean;
}

export default function AdaptiveLoginForm({ callbackUrl = "/account" }: AdaptiveLoginFormProps) {
  const [errorMessage, setErrorMessage] = useState("");
  const [currentImageLoaded, setCurrentImageLoaded] = useState(false);
  const router = useRouter();
  const searchParams = useSearch({ strict: false });

  // Extract parameters from URL
  const from = (searchParams as any)?.from;
  const authProvider = (searchParams as any)?.auth_provider;
  const error = (searchParams as any)?.error;
  const isFromMobile = from === 'mobile';

  // Handle error parameters and show toast notifications
  useEffect(() => {
    if (error === 'auth_failed') {
      toast.error("Account not found. Please sign up using the mobile app first.", {
        duration: 5000,
        description: "You need to create an account through our mobile app before signing in to the web dashboard."
      });

      // Clear the error parameter from URL after showing toast
      router.navigate({
        to: "/login",
        search: { from, auth_provider: authProvider },
        replace: true
      });
    }
  }, [error, router, from, authProvider]);

  // Handler for auth success
  const handleAuthSuccess = () => {
    router.navigate({ to: callbackUrl });
  };

  // Handler for auth errors
  const handleAuthError = (error: Error) => {
    setErrorMessage(`Authentication failed: ${error.message || "Unknown error"}`);
  };

  // Handler to go back to mobile app
  const handleBackToMobile = () => {
    const mobileUrl = "mobile://back";
    window.location.href = mobileUrl;
    setTimeout(() => {
      setErrorMessage("Please return to the mobile app to continue.");
    }, 1000);
  };

  // Define available OAuth providers
  const availableProviders: OAuthProvider[] = [
    {
      id: 'google' as const,
      name: 'Google',
      component: <GoogleLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile || authProvider === 'google' || authProvider === 'all'
    },
    {
      id: 'github' as const,
      name: 'GitHub',
      component: <GithubLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile // Only show GitHub for direct web access
    },
    {
      id: 'apple' as const,
      name: 'Apple',
      component: (
        <div className="text-center">
          <p className="text-sm text-[#7e7b76] mb-1 font-manrope_1">Apple Sign In</p>
          <p className="text-xs text-[#7e7b76] opacity-70 font-manrope_1">
            Only available on iOS devices
          </p>
        </div>
      ),
      available: isFromMobile && (authProvider === 'apple' || authProvider === 'all')
    }
  ].filter(provider => provider.available);

  // Track when the image is loaded
  useEffect(() => {
    setCurrentImageLoaded(false);
    const img = new Image();
    img.src = "/MiloSignin.png";
    img.onload = () => setCurrentImageLoaded(true);
    img.onerror = () => {
      console.warn("Failed to load MiloSignin.png image");
      setCurrentImageLoaded(true);
    };
  }, []);

  return (
    <div className="w-full min-h-screen relative bg-[#e9e5dc] dark:bg-[#1e1b16]">
      {/* Navigation bar - matching account pages */}
      {isFromMobile && (
        <div className="fixed h-20 top-0 md:top-7 right-0 left-0 md:left-3 md:right-4 w-full md:w-[38%] backdrop-blur-sm justify-between px-4 z-[100005] items-center gap-2 py-1">
          <div className="flex items-center justify-between">
            <div className="font-manrope_1 flex gap-4 items-center">
              <span className="text-black dark:text-white text-sm md:text-base font-manrope_1 font-bold">Mobile Login</span>
            </div>
            <button
              type="button"
              onClick={handleBackToMobile}
              className="flex items-center text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm md:text-base font-manrope_1"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Mobile App
            </button>
          </div>
        </div>
      )}

      <div className="w-full h-full max-w-[1600px] mx-auto px-4 md:px-8 py-12 lg:py-8 flex flex-col lg:flex-row items-start gap-12 lg:gap-8 relative z-10">
        {/* Spacer div for fixed sidebar on larger screens */}
        <div className="hidden lg:block lg:w-2/4 lg:max-w-[30%]"></div>

        {/* Sidebar - fixed on larger screens */}
        <div className="w-full lg:w-2/4 text-left mt-4 lg:mt-[10rem] lg:fixed lg:max-w-[34%]">
          <div className="story-margin" id="login-content">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-black dark:from-white via-gray-700 dark:via-gray-300 to-gray-600 dark:to-gray-400 py-4 font-manrope_1">
              {isFromMobile ? (
                <>Welcome <span className="italic">back</span></>
              ) : (
                <>Sign <span className="italic">in</span></>
              )}
            </h1>
            <p className="text-sm md:text-lg text-[#7e7b76] font-manrope_1 max-w-full mx-auto lg:mx-0">
              {isFromMobile
                ? "Continue your fitness journey from where you left off in the mobile app"
                : "Secure access for existing members to the admin dashboard and premium features"
              }
            </p>
          </div>
        </div>

        {/* Main content */}
        <div className="w-full lg:w-3/4 relative lg:pr-0 lg:flex lg:flex-col lg:items-end lg:z-30">
          {/* Login content in card format */}
          <div className="lg:w-[54vw] lg:float-right lg:mr-0 relative lg:max-h-[calc(100vh-0px)] rounded-xl shadow-md dark:shadow-white/10">

            {/* Login content wrapper - matching PreOnboardingSteps structure */}
            <div className="w-full overflow-hidden rounded-xl">
              {/* Header section - matching PreOnboardingSteps */}
              <div className="flex items-center gap-4 px-6 md:px-8 py-3 bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-t-xl">
                <div className="text-[#7e7b76] text-xs text-left md:text-sm font-manrope_1 italic">
                  {isFromMobile
                    ? "Continue your journey from the mobile app..."
                    : "Secure access for existing members only..."
                  }
                </div>
              </div>

              {/* Content area - matching PreOnboardingSteps */}
              <div>
                {/* Image section - matching PreOnboardingSteps */}
                <div className="bg-[#e9e5dc] dark:bg-[#1e1b16] onboarding-image-container overflow-hidden">
                  <div className="w-full flex items-center justify-center relative">
                    {/* Placeholder that matches the background */}
                    <div
                      className={`absolute inset-0 bg-[#1e1b16] z-10 transition-opacity duration-300 ${
                        currentImageLoaded ? "opacity-0" : "opacity-100"
                      }`}
                    />

                    {/* Actual image */}
                    <img
                      src="/MiloSignin.png"
                      alt="Milo Sign In"
                      className="w-full h-auto max-h-[300px] sm:max-h-[350px] md:max-h-[400px] lg:max-h-[calc(100vh-356px)] opacity-80 onboarding-image relative z-[2]"
                      style={{ opacity: currentImageLoaded ? 0.8 : 0 }}
                      onLoad={() => setCurrentImageLoaded(true)}
                    />
                  </div>
                </div>

                {/* Text and authentication section - matching PreOnboardingSteps */}
                <div className="bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-b-xl">
                  {/* Error message */}
                  {errorMessage && (
                    <div className="w-full p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 mx-4 mt-4">
                      <p className="text-red-700 dark:text-red-300 text-sm font-manrope_1">{errorMessage}</p>
                    </div>
                  )}

                  {/* Text content - matching PreOnboardingSteps */}
                  <div className="flex flex-col font-manrope_1 items-start justify-center px-4 pt-8 mx-auto w-full max-w-[calc(100%-1rem)] md:max-w-[calc(100%-2rem)]">
                    <h1 className="text-lg font-semibold text-[#7e7b76] mb-2">
                      {isFromMobile ? "Continue from Mobile" : "Choose Sign-in Method"}
                    </h1>
                    <p className="text-[#7e7b76] text-left text-sm mb-6">
                      {isFromMobile ? (
                        authProvider === 'apple' ? "Sign in with the same Apple account you used in the mobile app" :
                        authProvider === 'google' ? "Sign in with the same Google account you used in the mobile app" :
                        authProvider === 'all' ? "Sign in with any method since you have multiple authentication options set up" :
                        "Continue your fitness journey from where you left off in the mobile app"
                      ) : (
                        "Select your preferred authentication method to access the admin dashboard"
                      )}
                    </p>

                    {/* OAuth Buttons Section - All providers visible */}
                    <div className="w-full mb-8">
                      {/* Mobile: Stack vertically, Desktop: Max 3 per row */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-2xl">
                        {availableProviders.map((provider) => (
                          <div key={provider.id} className="w-full">
                            {provider.component}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />

    </div>
  );
}