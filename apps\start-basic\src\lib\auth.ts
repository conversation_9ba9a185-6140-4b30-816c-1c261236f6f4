import { betterAuth, BetterAuthOptions } from "better-auth";
import { admin, customSession } from "better-auth/plugins";
import { convex, createConvexAdapter, api } from "database";
import * as z from "zod";
import { env } from "./env";
import { reactStartCookies } from "better-auth/react-start";
import { expo } from "@better-auth/expo";
import { googlePolyAuth } from "google-polyauth/server";
import UserRoleService from "./user-role-service";
import hybridCacheStorage from "./cache-adapter";
import { mapAppleProfileToUser } from "./utils";

const isDevEnvironment = process.env.NODE_ENV === "development";

const options = {
  appName: "neb-starter",
  database: (() => {
    try {
      console.log("Attempting to create Convex adapter...");
      const adapter = createConvexAdapter(convex);
      console.log("Convex adapter created successfully");
      return adapter;
    } catch (error) {
      console.error("Failed to create Convex adapter:", error);
      throw error;
    }
  })(),
  baseURL: env.VITE_APP_URL,
  secret: env.BETTER_AUTH_SECRET,
  trustedOrigins: [
    env.VITE_APP_URL,
    "mobile://",
    "exp+neb-mobile://",
    "exp+mobile://expo-development-client",
    ...(isDevEnvironment ? ["http://localhost:3000"] : []),
  ],
  secondaryStorage: hybridCacheStorage,
  session: {
    freshAge: 0,
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5 minutes
    },
  },
  user: {
    additionalFields: {
      role: {
        type: "string",
        required: false,
        defaultValue: "user",
        input: false,
        validator: {
          input: z.enum(["user", "admin"]),
          output: z.enum(["user", "admin"]),
        },
      },
      firstName: {
        type: "string",
        required: true,
      },
      lastName: {
        type: "string",
        required: true,
      },
    },
  },
  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["google", "github"],
    },
  },
  socialProviders: {
    ...(env.APPLE_CLIENT_ID && env.APPLE_CLIENT_SECRET && env.APPLE_APP_BUNDLE_IDENTIFIER ? {
      apple: {
        clientId: env.APPLE_CLIENT_ID,
        clientSecret: env.APPLE_CLIENT_SECRET,
        appBundleIdentifier: env.APPLE_APP_BUNDLE_IDENTIFIER,
        mapProfileToUser: mapAppleProfileToUser,
      },
    } : {}),
    ...(env.GITHUB_CLIENT_ID && env.GITHUB_CLIENT_SECRET ? {
      github: {
        clientId: env.GITHUB_CLIENT_ID,
        clientSecret: env.GITHUB_CLIENT_SECRET,
        mapProfileToUser: (profile) => {
          return {
            firstName: profile.name.split(" ")[0],
            lastName: profile.name.split(" ")[1],
          };
        },
      },
    } : {}),
  },
  databaseHooks: {
    user: {
      create: {
        before: async (user, context) => {
          // Check if this is a web request (not from mobile app)
          const isWebRequest = context?.request && !context.request.headers.get('user-agent')?.includes('Expo') &&
                               !context.request.headers.get('x-expo-platform') &&
                               !context.request.url?.includes('mobile://') &&
                               !context.request.url?.includes('exp+');

          // If this is a web request, check if user already exists
          if (isWebRequest) {
            console.log("[AUTH HOOK] Web request detected, checking if user exists:", user.email);

            try {
              // Check if user exists in database
              const existingUser = await convex.query(api.users.getByEmail, { email: user.email });

              if (!existingUser) {
                console.log("[AUTH HOOK] User does not exist, blocking creation from web");
                throw new Error("Account not found. Please sign up using the mobile app first.");
              }

              console.log("[AUTH HOOK] Existing user found, allowing sign-in");
            } catch (error) {
              if (error instanceof Error && error.message.includes("Account not found")) {
                throw error;
              }
              console.error("[AUTH HOOK] Error checking user existence:", error);
              throw new Error("Authentication failed. Please try again.");
            }
          }

          // Process user data for name formatting
          let processedUser = { ...user } as any;

          // Check if name contains a space (likely a real name)
          if (user.name && user.name.includes(" ")) {
            processedUser = {
              ...processedUser,
              firstName: user.name.split(" ")[0],
              lastName: user.name.split(" ")[1] || "User",
            };
          }
          // If the name looks like an email (contains @ but no spaces)
          else if (user.name && user.name.includes("@") && !user.name.includes(" ")) {
            const username = user.name.split("@")[0];
            const formattedUsername = username.charAt(0).toUpperCase() + username.slice(1);

            processedUser = {
              ...processedUser,
              firstName: formattedUsername,
              lastName: "User",
            };
          }
          // Default fallback case
          else {
            processedUser = {
              ...processedUser,
              firstName: user.name || "New",
              lastName: user.name || "User",
            };
          }

          return {
            data: processedUser,
          };
        },
      },
    },
  },
  plugins: [
    admin(),
    expo(),
    googlePolyAuth({
      clientId: env.GOOGLE_WEB_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
      additionalClientIds: [env.GOOGLE_IOS_CLIENT_ID, env.GOOGLE_ANDROID_CLIENT_ID].filter(Boolean) as string[],
      scope: ["openid", "profile", "email"],
      redirectURI: env.GOOGLE_REDIRECT_URI,
    }),

    reactStartCookies(),
  ],
  onAPIError: {
    errorURL: "/login?error=auth_failed",
  },
  advanced: {
    generateId: false,
    crossSubDomainCookies: isDevEnvironment
      ? { enabled: false }
      : {
          enabled: true,
          domain: "neb-starter-web.vercel.app",
        },
    useSecureCookies: !isDevEnvironment ? true : false,
    cookies: {
      session_token: {
        attributes: {
          secure: !isDevEnvironment ? true : false,
          sameSite: "lax",
          ...(isDevEnvironment ? {} : { domain: ".neb-starter-web.vercel.app" }),
        },
      },
    },
  },
} satisfies BetterAuthOptions;

export const auth = betterAuth({
  ...options,
  plugins: [
    ...options.plugins,
    customSession(async (sessionData) => {
      if (!sessionData) {
        return sessionData;
      }
      const { user, session } = sessionData;
      // Get role from cache service
      const role = await UserRoleService.getUserRole(session.userId);

      return {
        user: {
          ...user,
          role,
        },
        session,
      };
    }, options),
  ],
});
